package com.pokecobble.town.config;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.config.ConfigSynchronizer;
import com.pokecobble.config.UserPreferencesManager;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages town-specific settings and synchronizes them between client and server.
 */
public class TownSettingsManager {
    private static TownSettingsManager instance;
    
    // Town settings storage (server-side)
    private static final Map<UUID, Map<String, Object>> townSettings = new HashMap<>();
    
    private TownSettingsManager() {
    }
    
    public static TownSettingsManager getInstance() {
        if (instance == null) {
            instance = new TownSettingsManager();
        }
        return instance;
    }
    
    /**
     * Gets town settings for a specific town.
     *
     * @param townId The ID of the town
     * @return Map of town settings
     */
    public static Map<String, Object> getTownSettings(UUID townId) {
        Map<String, Object> settings = townSettings.get(townId);
        if (settings == null) {
            settings = createDefaultTownSettings(townId);
            townSettings.put(townId, settings);
        }
        return new HashMap<>(settings);
    }

    /**
     * Gets existing town settings without creating defaults.
     * Used for saving to disk to avoid saving default values.
     *
     * @param townId The ID of the town
     * @return Map of existing town settings, or null if none exist
     */
    public static Map<String, Object> getExistingTownSettings(UUID townId) {
        Map<String, Object> settings = townSettings.get(townId);
        return settings != null ? new HashMap<>(settings) : null;
    }
    
    /**
     * Sets a town setting.
     *
     * @param townId The ID of the town
     * @param key The setting key
     * @param value The setting value
     */
    public static void setTownSetting(UUID townId, String key, Object value) {
        Map<String, Object> settings = townSettings.computeIfAbsent(townId, k -> createDefaultTownSettings(townId));
        settings.put(key, value);
        
        // Apply the setting to the town object
        applySettingToTown(townId, key, value);

        // Save the town to disk to persist the setting
        com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
        if (town != null) {
            com.pokecobble.town.data.TownDataStorage.saveTown(town);
            Pokecobbleclaim.LOGGER.debug("Saved town " + town.getName() + " to disk after setting " + key);

            // Sync updated settings to all online players
            try {
                net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
                if (server != null) {
                    com.pokecobble.config.ConfigSynchronizer.syncAllTownSettingsToAllPlayers(server);
                    Pokecobbleclaim.LOGGER.debug("Synced town setting change to all online players");
                } else {
                    Pokecobbleclaim.LOGGER.debug("Server not available, skipping town settings sync");
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Could not sync town settings to players after change: " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.debug("Set town setting " + key + " = " + value + " for town " + townId);
    }
    
    /**
     * Sets multiple town settings.
     *
     * @param townId The ID of the town
     * @param settings Map of settings to set
     */
    public static void setTownSettings(UUID townId, Map<String, Object> settings) {
        Map<String, Object> townSettingsMap = townSettings.computeIfAbsent(townId, k -> createDefaultTownSettings(townId));
        townSettingsMap.putAll(settings);
        
        // Apply all settings to the town object
        for (Map.Entry<String, Object> entry : settings.entrySet()) {
            applySettingToTown(townId, entry.getKey(), entry.getValue());
        }

        // Save the town to disk to persist the settings
        com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
        if (town != null) {
            com.pokecobble.town.data.TownDataStorage.saveTown(town);
            Pokecobbleclaim.LOGGER.debug("Saved town " + town.getName() + " to disk after settings update");

            // Sync updated settings to all online players
            try {
                net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
                if (server != null) {
                    com.pokecobble.config.ConfigSynchronizer.syncAllTownSettingsToAllPlayers(server);
                    Pokecobbleclaim.LOGGER.debug("Synced town settings change to all online players");
                } else {
                    Pokecobbleclaim.LOGGER.debug("Server not available, skipping town settings sync");
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Could not sync town settings to players after change: " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.debug("Set " + settings.size() + " town settings for town " + townId);
    }
    
    /**
     * Creates default town settings based on the town's current state.
     *
     * @param townId The ID of the town
     * @return Map of default settings
     */
    private static Map<String, Object> createDefaultTownSettings(UUID townId) {
        Map<String, Object> settings = new HashMap<>();

        // Get the town to read current values
        Town town = TownManager.getInstance().getTown(townId);
        if (town != null) {
            settings.put("isOpen", town.getJoinType() == Town.JoinType.OPEN);
            settings.put("allowPublicBuilding", false); // Default to false
            settings.put("enablePvP", false); // Default to false
            settings.put("allowMobSpawning", true); // Default to true
            settings.put("enableTownChat", true); // Default to true
            settings.put("showTownInList", true); // Default to true
        } else {
            // Default values if town not found
            settings.put("isOpen", true);
            settings.put("allowPublicBuilding", false);
            settings.put("enablePvP", false);
            settings.put("allowMobSpawning", true);
            settings.put("enableTownChat", true);
            settings.put("showTownInList", true);
        }

        return settings;
    }
    
    /**
     * Applies a setting to the actual town object.
     *
     * @param townId The ID of the town
     * @param key The setting key
     * @param value The setting value
     */
    private static void applySettingToTown(UUID townId, String key, Object value) {
        // Try to get town from TownManager first
        Town town = TownManager.getInstance().getTown(townId);

        // On client side, also try ClientTownManager
        if (town == null && net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
            try {
                town = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
            } catch (Exception e) {
                // ClientTownManager might not be available on server side
            }
        }

        if (town == null) {
            Pokecobbleclaim.LOGGER.warn("Cannot apply setting to town " + townId + " - town not found in TownManager or ClientTownManager");
            return;
        }

        try {
            switch (key) {
                case "isOpen":
                    boolean isOpen = (Boolean) value;
                    town.setJoinType(isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setJoinType(isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED);
                                Pokecobbleclaim.LOGGER.debug("Updated isOpen setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "allowPublicBuilding":
                    // Store the setting for future use when public building system is implemented
                    Pokecobbleclaim.LOGGER.debug("Public building setting updated: " + value);
                    break;
                case "enablePvP":
                    // Store the setting for future use when PvP system is implemented
                    Pokecobbleclaim.LOGGER.debug("PvP setting updated: " + value);
                    break;
                case "allowMobSpawning":
                    // Store the setting for future use when mob spawning control is implemented
                    Pokecobbleclaim.LOGGER.debug("Mob spawning setting updated: " + value);
                    break;
                case "enableTownChat":
                    // Store the setting for future use when town chat system is implemented
                    Pokecobbleclaim.LOGGER.debug("Town chat setting updated: " + value);
                    break;
                case "showTownInList":
                    // Store the setting for future use when town listing system is implemented
                    Pokecobbleclaim.LOGGER.debug("Town visibility setting updated: " + value);
                    break;
                default:
                    Pokecobbleclaim.LOGGER.warn("Unknown town setting: " + key);
                    break;
            }

            // Save the town after applying settings (only on server side)
            if (!net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                TownManager.getInstance().saveTown(town);
            } else {
                Pokecobbleclaim.LOGGER.debug("Applied town setting " + key + " = " + value + " to client-side town " + townId);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying town setting " + key + ": " + e.getMessage());
        }
    }
    
    /**
     * Gets the current player's town settings (client-side).
     *
     * @return Map of town settings, or empty map if player is not in a town
     */
    @Environment(EnvType.CLIENT)
    public static Map<String, Object> getCurrentPlayerTownSettings() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return new HashMap<>();
        }
        
        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        if (playerTown == null) {
            return new HashMap<>();
        }
        
        return getTownSettings(playerTown.getId());
    }
    
    /**
     * Updates the current player's town settings (client-side).
     *
     * @param key The setting key
     * @param value The setting value
     */
    @Environment(EnvType.CLIENT)
    public static void updateCurrentPlayerTownSetting(String key, Object value) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return;
        }

        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        if (playerTown == null) {
            Pokecobbleclaim.LOGGER.warn("Player is not in a town, cannot update town settings");
            return;
        }

        // Update local settings
        setTownSetting(playerTown.getId(), key, value);

        // Create a map with the single setting to send to server
        Map<String, Object> settingsUpdate = new HashMap<>();
        settingsUpdate.put(key, value);

        // Send the update to the server via the config synchronizer
        ConfigSynchronizer.sendConfigUpdate(ConfigSynchronizer.CATEGORY_TOWN, settingsUpdate);

        // Update user preferences to reflect the change
        UserPreferencesManager.getInstance().setPreference(ConfigSynchronizer.CATEGORY_TOWN, key, value);

        Pokecobbleclaim.LOGGER.debug("Updated town setting " + key + " = " + value + " for player's town");
    }
    
    /**
     * Syncs town settings to user preferences (client-side).
     *
     * @param townId The ID of the town
     */
    @Environment(EnvType.CLIENT)
    public static void syncTownSettingsToPreferences(UUID townId) {
        Map<String, Object> settings = getTownSettings(townId);
        UserPreferencesManager.getInstance().setCategoryPreferences(ConfigSynchronizer.CATEGORY_TOWN, settings);
        
        Pokecobbleclaim.LOGGER.debug("Synced town settings to user preferences for town " + townId);
    }
    
    /**
     * Clears town settings for a specific town.
     *
     * @param townId The ID of the town
     */
    public static void clearTownSettings(UUID townId) {
        townSettings.remove(townId);
        Pokecobbleclaim.LOGGER.debug("Cleared town settings for town " + townId);
    }

    /**
     * Debug method to test town settings persistence.
     */
    public static void testTownSettingsPersistence() {
        Pokecobbleclaim.LOGGER.info("=== Testing Town Settings Persistence ===");

        try {
            // Create a test town
            com.pokecobble.town.Town testTown = new com.pokecobble.town.Town("TestSettingsPersistence");
            UUID townId = testTown.getId();
            com.pokecobble.town.TownManager.getInstance().addTown(testTown, false);

            Pokecobbleclaim.LOGGER.info("Created test town: " + testTown.getName() + " with ID: " + townId);

            // Set some custom settings
            setTownSetting(townId, "isOpen", false);
            setTownSetting(townId, "allowPublicBuilding", true);
            setTownSetting(townId, "enablePvP", true);

            Map<String, Object> originalSettings = getTownSettings(townId);
            Pokecobbleclaim.LOGGER.info("Original settings: " + originalSettings);

            // Save to disk
            com.pokecobble.town.data.TownDataStorage.saveTown(testTown);
            Pokecobbleclaim.LOGGER.info("Saved town to disk");

            // Clear from memory (simulate server restart)
            clearTownSettings(townId);
            // Note: We can't easily remove from TownManager for testing, so we'll just clear settings

            // Load from disk
            com.pokecobble.town.Town loadedTown = com.pokecobble.town.data.TownDataStorage.loadTown(townId);
            if (loadedTown != null) {
                com.pokecobble.town.TownManager.getInstance().addTown(loadedTown, false);

                Map<String, Object> loadedSettings = getTownSettings(townId);
                Pokecobbleclaim.LOGGER.info("Loaded settings: " + loadedSettings);

                // Compare
                boolean matches = originalSettings.equals(loadedSettings);
                Pokecobbleclaim.LOGGER.info("Settings match: " + matches);

                if (!matches) {
                    Pokecobbleclaim.LOGGER.warn("Settings do not match!");
                    for (String key : originalSettings.keySet()) {
                        Object orig = originalSettings.get(key);
                        Object loaded = loadedSettings.get(key);
                        if (!orig.equals(loaded)) {
                            Pokecobbleclaim.LOGGER.warn("  " + key + ": " + orig + " -> " + loaded);
                        }
                    }
                }

                // Clean up
                clearTownSettings(townId);
            } else {
                Pokecobbleclaim.LOGGER.error("Failed to load town from disk!");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Test failed: " + e.getMessage());
            e.printStackTrace();
        }

        Pokecobbleclaim.LOGGER.info("=== Town Settings Persistence Test Complete ===");
    }
}
