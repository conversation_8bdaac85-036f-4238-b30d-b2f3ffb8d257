package com.pokecobble.town.server;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.PlayerDataManager;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.config.BackupConfig;
import com.pokecobble.town.data.PlayerDataUtils;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import net.fabricmc.api.DedicatedServerModInitializer;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

/**
 * Initializes server-side components of the mod.
 * This class is responsible for setting up the server instance in the TownManager
 * and registering server lifecycle events.
 */
public class ServerInitializer implements DedicatedServerModInitializer {
    // Track the last backup time
    private static Instant lastBackupTime = Instant.now();
    @Override
    public void onInitializeServer() {
        Pokecobbleclaim.LOGGER.info("Initializing PokeCobbleClaim server components");

        // Load backup configuration
        BackupConfig.load();

        // Register server start event
        ServerLifecycleEvents.SERVER_STARTING.register(this::onServerStarting);

        // Register server stop event
        ServerLifecycleEvents.SERVER_STOPPING.register(this::onServerStopping);

        // Register player join event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            ServerPlayerEntity player = handler.getPlayer();
            UUID playerId = player.getUuid();

            // Handle player join in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerJoin(player);

            // When a player joins, synchronize town and chunk data to them
            TownManager.getInstance().synchronizeTownData();
            ChunkDataSynchronizer.syncChunkData(server);

            // Send the current town list to the new player
            // This ensures they see all existing towns immediately, including loaded towns
            TownDataSynchronizer.sendTownListToNewPlayer(player);

            // Ensure all town data is synchronized to the new player
            // This is critical for showing loaded towns after server restart
            server.execute(() -> {
                // Sync all town data to the new player
                TownDataSynchronizer.syncPlayerTownData(server, playerId);

                // Ensure player's town membership is synchronized
                UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
                if (townId != null) {
                    // Player is in a town, ensure this is synchronized to client
                    Town playerTown = TownManager.getInstance().getTownById(townId);
                    if (playerTown != null) {
                        // Force synchronization of the player's town data, especially player ranks
                        TownDataSynchronizer.syncTownData(server, playerTown);

                        // Synchronize town settings to the player with a small delay to ensure all data is loaded
                        server.execute(() -> {
                            try {
                                // Small delay to ensure all town data is fully loaded
                                Thread.sleep(100);
                                com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                                Pokecobbleclaim.LOGGER.info("Synchronized town settings for player " + player.getName().getString() + " to town " + townId);
                            } catch (Exception e) {
                                Pokecobbleclaim.LOGGER.warn("Failed to sync town settings to player " + player.getName().getString() + ": " + e.getMessage());
                            }
                        });

                        Pokecobbleclaim.LOGGER.info("Synchronized town membership and player ranks for player " + player.getName().getString() + " to town " + townId);
                    }
                } else {
                    Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " is not in any town");

                    // Even if player is not in a town, sync all town settings so they can see all towns properly
                    server.execute(() -> {
                        try {
                            Thread.sleep(100);
                            com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                            Pokecobbleclaim.LOGGER.info("Synchronized all town settings for player " + player.getName().getString() + " (not in any town)");
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.warn("Failed to sync all town settings to player " + player.getName().getString() + ": " + e.getMessage());
                        }
                    });
                }

                // Log the number of towns available for this player
                int townCount = TownManager.getInstance().getAllTowns().size();
                Pokecobbleclaim.LOGGER.info("Synchronized " + townCount + " towns to player " + player.getName().getString());
            });
        });

        // Register player leave event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            // Handle player leave in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerLeave(handler.getPlayer());

            // When a player leaves, remove them from version tracking
            com.pokecobble.town.network.town.TownDataSynchronizer.removePlayer(handler.getPlayer().getUuid());
            ChunkDataSynchronizer.removePlayer(handler.getPlayer().getUuid());
            PlayerDataSynchronizer.removePlayer(handler.getPlayer().getUuid());

            // Clear status subscriptions
            com.pokecobble.status.RealTimeStatusManager.getInstance().clearPlayerSubscriptions(handler.getPlayer().getUuid());

            // Clear config subscriptions
            com.pokecobble.config.ConfigSynchronizer.clearPlayerConfig(handler.getPlayer().getUuid());
        });

        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim server components initialized successfully");
    }

    /**
     * Called when the server is starting.
     * Sets the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStarting(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Setting server instance in TownManager and PlayerDataManager");

        // Set server instance in TownManager
        TownManager.getInstance().setServer(server);

        // Set server instance in PlayerDataManager
        PlayerDataManager.getInstance().setServer(server);

        // Set server instance in ErrorLogger
        ErrorLogger.getInstance().setServer(server);
        ErrorLogger.getInstance().logInfo("Server started", "Server");

        // Initialize real-time status manager
        com.pokecobble.status.RealTimeStatusManager.getInstance().initialize(server);

        // Load town data from disk to ensure it's available when players join
        // This is critical for restoring player-town relationships after server restart
        Pokecobbleclaim.LOGGER.info("Loading town data during server startup");
        com.pokecobble.town.data.TownDataStorage.loadTowns();

        // After loading towns, ensure all town data is ready for synchronization
        // Broadcast town list to prepare for when players join
        TownManager townManager = TownManager.getInstance();
        if (!townManager.getAllTowns().isEmpty()) {
            Pokecobbleclaim.LOGGER.info("Preparing town data synchronization for " + townManager.getAllTowns().size() + " loaded towns");

            // Verify that town settings were properly loaded for all towns
            int townsWithSettings = 0;
            int townsWithCustomSettings = 0;
            for (com.pokecobble.town.Town town : townManager.getAllTowns()) {
                // Check if town has any existing settings (not defaults)
                java.util.Map<String, Object> existingSettings = com.pokecobble.town.config.TownSettingsManager.getExistingTownSettings(town.getId());
                if (existingSettings != null && !existingSettings.isEmpty()) {
                    townsWithCustomSettings++;
                }

                // This will create defaults if none exist, but we want to track actual loaded settings
                java.util.Map<String, Object> settings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
                if (settings != null && !settings.isEmpty()) {
                    townsWithSettings++;
                }
            }
            Pokecobbleclaim.LOGGER.info("Town settings loaded for " + townsWithSettings + " out of " + townManager.getAllTowns().size() + " towns (" + townsWithCustomSettings + " with custom settings)");

            // The actual broadcast will happen when players join, but this ensures data is ready
        } else {
            Pokecobbleclaim.LOGGER.info("No towns were loaded from disk - starting with empty town list");
        }

        // Reset all player data versions
        TownManager.getInstance().resetAllPlayerDataVersions();
        PlayerDataSynchronizer.clearVersionTracking();

        // Register periodic town data synchronization
        registerPeriodicSync(server);
    }

    /**
     * Called when the server is stopping.
     * Clears the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStopping(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Saving all data and clearing server instances");

        // Save all player data
        PlayerDataManager.getInstance().saveAllPlayers();

        // Save all town and player-town relationship data
        TownManager.getInstance().saveAllData();

        // Create backup if enabled
        if (BackupConfig.isBackupOnServerStopEnabled()) {
            Pokecobbleclaim.LOGGER.info("Creating player data backup on server stop");
            String backupPath = PlayerDataUtils.backupAllPlayerData();
            if (backupPath != null) {
                Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);

                // Clean up old backups
                int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                if (deleted > 0) {
                    Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                }
            } else {
                Pokecobbleclaim.LOGGER.warn("Failed to create backup on server stop");
            }
        }

        // Clear server instance in TownManager
        TownManager.getInstance().setServer(null);

        // Clear server instance in PlayerDataManager
        PlayerDataManager.getInstance().setServer(null);
    }

    /**
     * Registers periodic town data synchronization.
     * This ensures that town data is synchronized even if no explicit changes are made.
     *
     * @param server The server instance
     */
    private void registerPeriodicSync(MinecraftServer server) {
        // Register a tick callback to periodically synchronize town data
        net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents.END_SERVER_TICK.register(s -> {
            // Synchronize town, chunk, and player data every 5 minutes (6000 ticks)
            if (s.getTicks() % 6000 == 0) {
                TownManager.getInstance().synchronizeTownData();
                ChunkDataSynchronizer.syncChunkData(s);
                PlayerDataSynchronizer.syncAllPlayerData(s);

                // Save all player data to disk
                PlayerDataManager.getInstance().saveAllPlayers();

                // Check if it's time for a backup
                if (BackupConfig.isAutoBackupEnabled()) {
                    Instant now = Instant.now();
                    long hoursSinceLastBackup = ChronoUnit.HOURS.between(lastBackupTime, now);

                    if (hoursSinceLastBackup >= BackupConfig.getBackupIntervalHours()) {
                        Pokecobbleclaim.LOGGER.info("Creating scheduled player data backup");
                        String backupPath = PlayerDataUtils.backupAllPlayerData();

                        if (backupPath != null) {
                            Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);
                            lastBackupTime = now;

                            // Clean up old backups
                            int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                            if (deleted > 0) {
                                Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                            }
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Failed to create scheduled backup");
                        }
                    }
                }
            }

            // Update towns (elections, etc.) every second (20 ticks)
            if (s.getTicks() % 20 == 0) {
                TownManager.getInstance().update();
            }

            // Check watching players' balances every 5 seconds (100 ticks)
            // The internal check in MoneyNetworkHandler will limit the actual check frequency
            // and handle errors gracefully to prevent lag and rate limiting
            if (s.getTicks() % 100 == 0) {
                com.pokecobble.town.network.money.MoneyNetworkHandler.checkWatchingPlayersBalances(s);
            }
        });
    }
}
